module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def get_prefixed_cache_key(account_id, key)
    "idb-cache-key-account-#{account_id}-#{key}"
  end

  def fetch_value_for_key(account_id, key)
    prefixed_cache_key = get_prefixed_cache_key(account_id, key)
    value_from_cache = Redis::Alfred.get(prefixed_cache_key)

    return value_from_cache if value_from_cache.present?

    # zero epoch time: 1970-01-01 00:00:00 UTC
    '**********'
  end
end
