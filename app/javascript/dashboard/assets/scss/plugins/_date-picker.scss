@import 'vue-datepicker-next/scss/index';

.date-picker {
  // To be removed one SLA reports date picker is created
  &.small {
    .mx-input {
      @apply h-8 text-sm;
    }
  }

  &.no-margin {
    .mx-input {
      margin-bottom: 0 !important;
    }
  }

  &:not(.auto-width) {
    .mx-datepicker-range {
      @apply w-[320px];
    }
  }

  .mx-datepicker {
    @apply w-full;
  }

  .mx-input {
    @apply h-[2.5rem] flex border border-solid border-n-weak rounded-md shadow-none;
  }

  .mx-input:disabled,
  .mx-input[readonly] {
    @apply bg-n-background cursor-pointer;
  }

  .mx-icon-calendar {
    @apply text-n-slate-10;
  }
}

.mx-datepicker-main {
  @apply border-0 bg-n-solid-2 rounded-xl;

  .cell {
    &.disabled {
      @apply bg-n-slate-2 dark:bg-n-background text-n-slate-10;
    }

    &:hover,
    &.hover-in-range,
    &.in-range {
      @apply bg-n-slate-3 dark:bg-n-solid-3 text-n-slate-12;
    }
  }

  .mx-calendar + .mx-calendar {
    @apply border-l border-n-weak;
  }

  .mx-datepicker-footer {
    @apply border border-n-weak;
  }

  .mx-time {
    @apply border-0 bg-n-background dark:bg-n-solid-2;

    .mx-time-header {
      @apply border-0;
    }

    .mx-time-item {
      &.disabled {
        @apply bg-n-slate-2 dark:bg-n-background;
      }

      &:hover {
        @apply bg-n-slate-3 dark:bg-n-solid-3;
      }
    }
  }

  .today {
    @apply font-semibold;
  }
}

.mx-datepicker-popup {
  @apply z-[99999];
}

.mx-datepicker-inline {
  @apply w-full;

  .mx-calendar {
    @apply w-full;
  }
}
