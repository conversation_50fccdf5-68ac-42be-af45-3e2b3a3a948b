## 项目分析报告（Chatwoot）

### 1. 项目概述
- **项目名称**: Chatwoot
- **用途/主要功能**: 开源的全渠道客户支持平台，聚合网站在线客服、Email、Facebook、Instagram、Twitter、WhatsApp、Telegram、Line、SMS 等渠道消息至统一的客服收件箱，支持自动化、团队协作、帮助中心、统计报表与 AI 助手（Captain）。
- **项目类型**: Web 应用 + API 服务（单体 Rails 应用，前后端同仓）
- **目标用户群体**: 企业/团队的客服、运营、售后支持人员，以及自建客服系统的开发与运维团队。

参考：
```1:141:README.md
# Chatwoot
...（README 展示了产品定位、特性与部署选项，略）
```

---

### 2. 技术栈分析
- **前端**
  - **框架/库**: Vue 3、Vue Router、Vuex、Axios、Tailwind（样式）、ActionCable JS 客户端
  - **构建/开发**: Vite 5、vite-plugin-ruby、Vitest、Histoire（组件故事）
  - 证据：
    ```1:34:package.json
    "dependencies": {
      "vue": "^3.5.12",
      "vue-router": "~4.4.5",
      "vuex": "~4.1.0",
      "axios": "^1.8.2",
      "@vitejs/plugin-vue": "^5.1.4",
      "@rails/actioncable": "6.1.3"
    }
    ```
    ```1:112:vite.config.ts
    export default defineConfig({
      plugins: plugins,
      resolve: {
        alias: {
          vue: 'vue/dist/vue.esm-bundler.js',
          components: path.resolve('./app/javascript/dashboard/components'),
          widget: path.resolve('./app/javascript/widget')
        }
      },
      test: { environment: 'jsdom', ... }
    })
    ```
- **后端**
  - **语言/框架**: Ruby 3.4.4、Ruby on Rails 7.1（API + 视图渲染 + ActionCable）
  - **数据存储**: PostgreSQL（含 pgvector/neighbor，为 AI/向量检索支持）、ActiveStorage；Redis（缓存、ActionCable、任务队列键、限流等）
  - **队列/任务**: Sidekiq 7 + sidekiq-cron，ActiveJob 适配
  - **认证/授权**: Devise + devise_token_auth（Token 登录）、OmniAuth（Google）、Pundit（权限策略）
  - **可观测**: Sentry、Datadog、Elastic APM、Scout（按需加载）
  - 证据：
    ```1:246:Gemfile
    ruby '3.4.4'
    gem 'rails', '~> 7.1'
    gem 'pg'
    gem 'pgvector'
    gem 'redis'
    gem 'sidekiq', '>= 7.3.1'
    gem 'sidekiq-cron', '>= 1.12.0'
    gem 'devise', '>= 4.9.4'
    gem 'devise_token_auth', '>= 1.2.3'
    gem 'pundit'
    gem 'vite_rails'
    ```
- **开发工具/构建系统**
  - Node 23.x、pnpm 10.x、Foreman/Overmind 启动多进程、Rubocop、ESLint、Vitest、RSpec
  - 证据：
    ```1:171:package.json
    "engines": { "node": "23.x", "pnpm": "10.x" }
    "scripts": { "start:dev": "foreman start -f ./Procfile.dev", "dev": "overmind start -f ./Procfile.dev" }
    ```
- **部署/运维**
  - Docker Compose（dev/prod/test）、Heroku、DigitalOcean K8s 一键部署
  - 环境变量开关可观测与第三方集成；Sidekiq、ActionCable 使用 Redis；PostgreSQL 使用 pgvector 镜像
  - 证据：
    ```1:120:docker-compose.yaml
    services:
      rails: ... depends_on: [postgres, redis, vite, mailhog, sidekiq]
      sidekiq: ...
      vite: ...
      postgres: image: pgvector/pgvector:pg16
      redis: image: redis:alpine
    ```

---

### 3. 项目架构
- **整体模式**: 单体 Rails 应用（MVC），前端 Vue 作为 SPA 由 `vite_rails` 注入构建产物；API、WebSocket、后台任务均在同体内。
- **核心子系统**:
  - API 服务：`/api/v1`、`/api/v2`、`/platform/api/v1`、`/public/api/v1`（Widget/公开接口）
  - WebSocket：ActionCable + Redis（连接 `RoomChannel` 推送在线状态/事件）
  - 任务队列：ActiveJob -> Sidekiq（异步发送邮件/消息派发/计划任务）
  - 前端：Dashboard、Widget、SDK（IIFE library mode 构建）
- **目录结构速览**（仅列关键）：
  - `app/controllers`：REST API 控制器；`api/v1` 下按 Account/Conversation/Message 等聚合
  - `app/models`：`Account`、`User`、`Inbox`、`Conversation`、`Message`、`Contact` 等领域模型
  - `app/channels`：ActionCable `RoomChannel`
  - `app/javascript`：前端 Vue 代码（dashboard、widget、entrypoints）
  - `config`：`routes.rb`、`database.yml`、`cable.yml`、`sidekiq.yml`、`vite.json`、大量 initializers
  - `lib/redis`、`lib/online_status_tracker.rb`：Redis 配置与在线状态追踪
  - `swagger/`：OpenAPI 文档
- **架构图（Mermaid）**
```mermaid
flowchart LR
  subgraph Browser
    A[Vue 3 SPA<br/>Dashboard/Widget]
  end
  subgraph Rails[Rails 7.1 Monolith]
    B[Controllers / API]
    C[Models]
    D[ActionCable]
    E[ActiveJob]
    F[Views / ViteRails]
  end
  subgraph Infra
    G[(PostgreSQL\npgvector)]
    H[(Redis)]
    I[Sidekiq]
  end

  A -- HTTP/JSON --> B
  A -- WebSocket --> D
  F -- Built by --> A
  B -- AR --> C
  C <-- SQL --> G
  E --> I
  I <--> H
  D <--> H
  B <--> H
```

- **消息数据流（简化）**
```mermaid
sequenceDiagram
  participant UI as Vue App
  participant API as Rails API
  participant M as Message(Model)
  participant J as ActiveJob
  participant Q as Sidekiq/Redis
  participant EXT as External Channels/Email

  UI->>API: POST /api/v1/accounts/:id/conversations/:cid/messages
  API->>M: MessageBuilder.perform / Message.create!
  M->>M: after_create_commit callbacks
  M->>J: SendReplyJob.perform_later
  J->>Q: Enqueue to Sidekiq
  Q->>EXT: Deliver via channel/email
  API-->>UI: 200 + message payload
  M-->>UI: ActionCable broadcast (message.created)
```

---

### 4. 数据结构
- **数据库设计/关系**（核心实体）
  - `Account` 账户：拥有 `Inbox`（渠道）、`Contact`、`Conversation`、`Message` 等
  - `User` 用户：与 `Account` 通过 `AccountUser` 关联（角色、可见性）
  - `Inbox` 渠道：多态关联具体渠道（Email、WebWidget、API、TwilioSms 等）
  - `Conversation` 会话：关联 `Account`、`Inbox`、`Contact`，包含多个 `Message`
  - `Message` 消息：incoming/outgoing/activity/template；`content_type` 多态（文本、表单、CSAT、邮件等）
  - `Contact` 访客/客户：可通过 `ContactInbox` 归属不同 `Inbox`
- **实体关系图（Mermaid ER）**
```mermaid
erDiagram
  ACCOUNT ||--o{ ACCOUNT_USER : has
  USER ||--o{ ACCOUNT_USER : has
  ACCOUNT ||--o{ INBOX : has
  INBOX ||--o{ CONVERSATION : has
  CONTACT ||--o{ CONVERSATION : has
  CONVERSATION ||--o{ MESSAGE : has
  ACCOUNT ||--o{ CONTACT : has
  INBOX ||--o{ MESSAGE : has
  USER ||--o{ MESSAGE : sends
  CONTACT ||--o{ MESSAGE : sends
  INBOX ||--o{ CONTACT_INBOX : has
  CONTACT ||--o{ CONTACT_INBOX : has
```
- **模型示例（包含校验与回调）**
  ```52:121:app/models/Conversation.rb
  class Conversation < ApplicationRecord
    validates :account_id, :inbox_id, :contact_id, presence: true
    enum status: { open: 0, resolved: 1, pending: 2, snoozed: 3 }
    has_many :messages, dependent: :destroy_async
    after_create_commit :notify_conversation_creation
    ...
  end
  ```
  ```41:132:app/models/Message.rb
  class Message < ApplicationRecord
    enum message_type: { incoming: 0, outgoing: 1, activity: 2, template: 3 }
    enum content_type: { text: 0, input_text: 1, input_textarea: 2, ... }
    after_create_commit :execute_after_create_commit_callbacks
    def execute_after_create_commit_callbacks
      reopen_conversation
      notify_via_mail
      set_conversation_activity
      dispatch_create_events
      send_reply
    end
  end
  ```
- **API 接口设计**
  - 路由组织（会话与消息相关）
    ```101:113:config/routes.rb
    resources :conversations, only: [:index, :create, :show, :update, :destroy] do
      scope module: :conversations do
        resources :messages, only: [:index, :create, :destroy, :update] do
          member do
            post :translate
            post :retry
          end
        end
      end
    end
    ```
  - OpenAPI/Swagger：`swagger/index.yml` 定义服务、鉴权与分类，`swagger/paths` 下按资源拆分
    ```1:36:swagger/index.yml
    openapi: '3.0.4'
    info: { title: Chatwoot, description: API 文档 }
    components.securitySchemes.userApiKey: { type: apiKey, in: header, name: api_access_token }
    ```
- **配置文件结构**
  - 数据库：
    ```1:9:config/database.yml
    default: &default
      adapter: postgresql
      host: <%= ENV['POSTGRES_HOST'] %>
      pool: <%= Sidekiq.server? ? ENV['SIDEKIQ_CONCURRENCY'] : ENV['RAILS_MAX_THREADS'] %>
    ```
  - WebSocket/ActionCable：
    ```1:8:config/cable.yml
    default:
      adapter: redis
      url: <%= ENV['REDIS_URL'] %>
      channel_prefix: <%= "chatwoot_#{Rails.env}_action_cable"  %>
    ```
  - Sidekiq：
    ```1:17:config/initializers/sidekiq.rb
    Sidekiq.configure_client { |c| c.redis = Redis::Config.app }
    Sidekiq.configure_server { |c| c.redis = Redis::Config.app }
    ```
  - Vite：端口与输出目录见 `config/vite.json`，构建细节见 `vite.config.ts`（SDK 使用 library mode IIFE）

---

### 5. 关键功能模块
- **核心业务：消息创建与投递**
  - 控制器到模型与任务队列的链路：
    ```45:56:config/routes.rb
    POST /api/v1/accounts/:account_id/conversations/:conversation_id/messages
    ```
    ```263:321:app/models/Message.rb
    def send_reply
      attachments.blank? ? ::SendReplyJob.perform_later(id)
                         : ::SendReplyJob.set(wait: 2.seconds).perform_later(id)
    end
    ```
- **用户认证**
  - Devise + devise_token_auth（Token 头）+ OmniAuth（Google）
    ```53:62:app/models/User.rb
    devise :database_authenticatable, :registerable, :recoverable, :rememberable,
           :trackable, :validatable, :confirmable, :password_has_required_content,
           :omniauthable, omniauth_providers: [:google_oauth2]
    ```
  - 路由挂载：
    ```1:9:config/routes.rb
    mount_devise_token_auth_for 'User', at: 'auth', controllers: { ... }
    ```
- **授权**
  - Pundit：`ApplicationController` 引入 `Pundit::Authorization`，`app/policies/*` 管理资源权限策略
- **数据处理流程**
  - 消息 after_create_commit 触发：会话状态维护、事件分发、邮件通知、派发异步发送
- **实时通信（在线状态/事件推送）**
  - ActionCable + Redis，`RoomChannel` 广播在线状态
    ```19:25:app/channels/room_channel.rb
    def broadcast_presence
      data = { account_id: @current_account.id, users: ::OnlineStatusTracker.get_available_users(@current_account.id) }
      data[:contacts] = ::OnlineStatusTracker.get_available_contacts(@current_account.id) if @current_user.is_a? User
      ActionCable.server.broadcast(pubsub_token, { event: 'presence.update', data: data })
    end
    ```
- **第三方集成**
  - Webhooks/回调：Twitter/Telegram/Line/WhatsApp/Instagram、Slack、Shopify、Linear、Dyte、Notion 等
    ```472:505:config/routes.rb
    mount Facebook::Messenger::Server, at: 'bot'
    get 'webhooks/twitter' => 'api/v1/webhooks#twitter_crc'
    post 'webhooks/telegram/:bot_token' => 'webhooks/telegram#process_payload'
    post 'webhooks/whatsapp/:phone_number' => 'webhooks/whatsapp#process_payload'
    ...
    ```

---

### 6. 开发与部署
- **本地开发环境搭建**
  - 运行环境：Ruby 3.4.4、Node 23.x、pnpm 10.x、PostgreSQL、Redis
  - 依赖安装（使用国内镜像源）
    - RubyGems（任选一种）：
      - `bundle config set mirror.https://rubygems.org https://gems.ruby-china.com`
      - 或替换源：`bundle config set --global BUNDLE_SSL_VERIFY_MODE 0` + 私有镜像（按需）
    - pnpm/npm：`pnpm config set registry https://registry.npmmirror.com`（或 `npm config set registry https://registry.npmmirror.com`）
  - 安装与启动：
    - `bundle install`
    - `pnpm install`
    - 开发启动（Foreman/Overmind 任一）：
      - `pnpm dev`（Overmind）或 `pnpm start:dev`（Foreman）
      - Procfile（3000 端口 Rails、Sidekiq、Vite 开发服务器）
        ```1:5:Procfile.dev
        backend: bin/rails s -p 3000
        worker: dotenv bundle exec sidekiq -C config/sidekiq.yml
        vite: bin/vite dev
        ```
- **测试策略**
  - 前端：Vitest（jsdom）、@vue/test-utils；覆盖率输出 `lcov` + `text`
  - 后端：RSpec（广泛的 `spec/models`、`spec/controllers`、`spec/jobs` 等），Rubocop 规则
  - Lint：ESLint（Airbnb base + Vue 3）、Rubocop（Rails/Performance/RSpec 插件）
  - 证据：`package.json` scripts、`spec/` 目录结构、`vitest.setup.js`
- **构建与部署流程**
  - Vite 构建：
    - 常规：`bin/vite build`（dashboard/widget 等入口）
    - SDK（IIFE 单文件）：`BUILD_MODE=library vite build` 输出至 `public/packs/js/sdk.js`
    ```21:73:vite.config.ts
    const isLibraryMode = process.env.BUILD_MODE === 'library';
    build: { rollupOptions: { output: { inlineDynamicImports: isLibraryMode } },
             lib: isLibraryMode ? { entry: './app/javascript/entrypoints/sdk.js', formats: ['iife'], name: 'sdk' } : undefined }
    ```
  - Docker Compose：开发/测试/生产均提供样例编排（含 Postgres/Redis）
  - 部署目标：Heroku 一键、DigitalOcean 1-Click K8s、其它方式参见官方部署文档
- **环境配置**（示例）
  - 数据库：`POSTGRES_HOST/PORT/USERNAME/PASSWORD/DATABASE`，`POSTGRES_STATEMENT_TIMEOUT`
  - Redis：`REDIS_URL`、`REDIS_PASSWORD`、`REDIS_OPENSSL_VERIFY_MODE`
  - Rails 可观测：`SENTRY_DSN`、`DD_TRACE_AGENT_URL`、`ELASTIC_APM_SECRET_TOKEN`、`SCOUT_KEY`
  - 其他：`FRONTEND_URL`、`CW_API_ONLY_SERVER`、`LOG_LEVEL`、`RAILS_MAX_THREADS`、`SIDEKIQ_CONCURRENCY`

---

### 附：运行时关键代码摘录
- **ActionCable（Redis 适配）**
  ```1:7:config/cable.yml
  default:
    adapter: redis
    url: <%= ENV.fetch('REDIS_URL', 'redis://127.0.0.1:6379') %>
    channel_prefix: <%= "chatwoot_#{Rails.env}_action_cable"  %>
  ```
- **Sidekiq 初始化**
  ```5:17:config/initializers/sidekiq.rb
  Sidekiq.configure_client do |config|
    config.redis = Redis::Config.app
  end
  Sidekiq.configure_server do |config|
    config.redis = Redis::Config.app
    if Rails.env.production?
      config.logger.formatter = Sidekiq::Logger::Formatters::JSON.new
      config[:skip_default_job_logging] = true
    end
  end
  ```
- **用户认证模块**
  ```53:62:app/models/User.rb
  devise :database_authenticatable, :registerable, :recoverable, :rememberable,
         :trackable, :validatable, :confirmable, :password_has_required_content,
         :omniauthable, omniauth_providers: [:google_oauth2]
  ```

---

### 任务清单（已完成）
- [x] 全局检索与关键文件定位（README、Gemfile、package.json、routes、models、Sidekiq/ActionCable/DB 配置、Swagger）
- [x] 整理技术栈与架构图（Mermaid）
- [x] 梳理数据模型与关系（Mermaid ER）
- [x] 汇总 API 设计与路由示例
- [x] 核心流程（消息创建/投递）的代码链路说明与示例
- [x] 开发环境与部署流程（含国内镜像源建议）
- [x] 生成 `项目分析报告.md`（本文件）
